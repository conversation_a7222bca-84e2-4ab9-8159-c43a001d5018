package com.yy.hd.mcp.service;

import com.yy.hd.commons.uri.HttpUris;
import lombok.Data;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
public class UserInfoService {

    private static final String QUERY_UID_URL = HttpUris.QUERY_UID_URI;

    private static final String QUERY_YY_URL = HttpUris.QUERY_YY_URI;

    private WebClient defaultWebClient;

    public Mono<Long> getUidByYY(String yy) {
        return defaultWebClient.get()
                .uri(QUERY_YY_URL + yy)
                .retrieve()
                .bodyToMono(Response.class)
                .map(res -> Long.parseLong(res.getData().toString()));
    }

    public Mono<Long> getYYByUid(long uid) {
        return defaultWebClient.get()
                .uri(QUERY_UID_URL + uid)
                .retrieve()
                .bodyToMono(Response.class)
                .map(res -> Long.parseLong(res.getData().toString()));
    }

    @Data
    static class Response<T> {

        private Integer result;

        private String msg;

        private T data;
    }
}
