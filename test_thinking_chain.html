<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>思维链滚动测试</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --radius-lg: 0.75rem;
      --radius-md: 0.5rem;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', 'Robot<PERSON>', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      height: 80vh;
      overflow-y: auto;
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 20px;
    }

    .content {
      height: 200vh; /* 创建足够的内容来测试滚动 */
    }

    .thinking-chain {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      margin: 1.5rem 0;
      overflow: hidden;
      transition: all 0.3s ease;
      position: relative;
      backdrop-filter: blur(10px);
      box-shadow: var(--shadow-sm);
    }

    .thinking-chain-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem 1.25rem;
      cursor: pointer;
      background: var(--bg-tertiary);
      border-bottom: 1px solid var(--border-primary);
      transition: all 0.3s ease;
      user-select: none;
      min-height: 56px;
    }

    .thinking-chain-header:hover {
      background: var(--bg-hover);
    }

    .thinking-chain-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--text-secondary);
      flex: 1;
    }

    .thinking-chain-icon {
      width: 18px;
      height: 18px;
      transition: all 0.3s ease;
      color: var(--text-tertiary);
      flex-shrink: 0;
    }

    .thinking-chain-header.expanded .thinking-chain-icon {
      transform: rotate(90deg);
      color: var(--accent-primary);
    }

    .thinking-chain-content {
      max-height: 0;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      background: var(--bg-primary);
    }

    .thinking-chain-content.expanded {
      max-height: 500px;
      overflow-y: auto;
      border-top: 1px solid var(--border-primary);
    }

    .thinking-chain-inner {
      padding: 1.25rem 1.5rem;
      color: var(--text-primary);
      font-size: 0.9rem;
      line-height: 1.7;
      white-space: pre-wrap;
      word-wrap: break-word;
      overflow-wrap: break-word;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    }

    .thinking-chain-badge {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      padding: 0.375rem 0.75rem;
      border-radius: var(--radius-md);
      font-size: 0.75rem;
      font-weight: 600;
      white-space: nowrap;
      box-shadow: var(--shadow-sm);
      transition: all 0.3s ease;
    }

    .test-content {
      background: var(--bg-tertiary);
      padding: 20px;
      margin: 20px 0;
      border-radius: var(--radius-md);
      border: 1px solid var(--border-primary);
    }
  </style>
</head>
<body>
  <div class="container" id="messageContainer">
    <div class="content">
      <h1>思维链滚动测试</h1>
      
      <div class="test-content">
        <p>这是一些测试内容，用来创建足够的页面高度来测试滚动功能。</p>
      </div>

      <div class="test-content">
        <p>更多测试内容...</p>
      </div>

      <div class="thinking-chain" data-thinking-id="test-thinking-1">
        <div class="thinking-chain-header" onclick="toggleThinkingChain('test-thinking-1')">
          <div class="thinking-chain-title">
            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>思维过程</span>
          </div>
          <span class="thinking-chain-badge">展开查看</span>
        </div>
        <div class="thinking-chain-content" id="test-thinking-1-content">
          <div class="thinking-chain-inner">这是思维链的内容。当点击展开时，页面应该滚动到思维链的位置，而不是滚动到页面顶部。

这里有更多的思维链内容来测试滚动效果。

让我们看看修复后的滚动功能是否正常工作。</div>
        </div>
      </div>

      <div class="test-content">
        <p>思维链后面的内容...</p>
      </div>

      <div class="test-content">
        <p>更多内容来测试滚动...</p>
      </div>

      <div class="thinking-chain" data-thinking-id="test-thinking-2">
        <div class="thinking-chain-header" onclick="toggleThinkingChain('test-thinking-2')">
          <div class="thinking-chain-title">
            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>第二个思维过程</span>
          </div>
          <span class="thinking-chain-badge">展开查看</span>
        </div>
        <div class="thinking-chain-content" id="test-thinking-2-content">
          <div class="thinking-chain-inner">这是第二个思维链的内容。

测试多个思维链的滚动效果。

确保每个思维链都能正确滚动到其位置。</div>
        </div>
      </div>

      <div class="test-content">
        <p>页面底部的内容...</p>
      </div>
    </div>
  </div>

  <script>
    // 修复后的滚动函数
    function smoothScrollToElement(element, offset = 0) {
      if (!element) return;

      const container = document.getElementById('messageContainer');
      
      // 获取滚动容器的边界矩形
      const containerRect = container.getBoundingClientRect();
      // 获取目标元素的边界矩形
      const elementRect = element.getBoundingClientRect();
      
      // 计算元素相对于滚动容器的位置
      const elementTop = elementRect.top - containerRect.top + container.scrollTop;
      const targetScrollTop = elementTop - offset;

      container.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
    }

    // 切换思维链展开/折叠状态
    function toggleThinkingChain(id) {
      const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
      const content = document.getElementById(`${id}-content`);
      const badge = header.querySelector('.thinking-chain-badge');
      const thinkingChain = header.closest('.thinking-chain');

      if (!header || !content || !badge) return;

      const isExpanded = header.classList.contains('expanded');

      if (isExpanded) {
        // 折叠
        header.classList.remove('expanded');
        content.classList.remove('expanded');
        badge.textContent = '展开查看';
      } else {
        // 展开
        header.classList.add('expanded');
        content.classList.add('expanded');
        badge.textContent = '收起';

        // 展开后自动滚动到思维链位置，确保内容可见
        setTimeout(() => {
          smoothScrollToElement(thinkingChain, 20);
        }, 300); // 等待展开动画完成
      }
    }

    // 将函数暴露到全局作用域
    window.toggleThinkingChain = toggleThinkingChain;
  </script>
</body>
</html>
